import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/tfi_questionnaire_controller.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

void main() {
  group('TFI Questionnaire Tests', () {
    late TFIQuestionnaireController controller;

    setUp(() {
      Get.testMode = true;
      controller = TFIQuestionnaireController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should handle meta question answer correctly', () {
      // Test YES answer
      controller.updateMetaAnswer(true);
      expect(controller.metaQuestionAnswer.value, true);
      expect(controller.showDetailedQuestions.value, true);
      expect(controller.getAnswer('metaQuest'), true);

      // Test NO answer
      controller.updateMetaAnswer(false);
      expect(controller.metaQuestionAnswer.value, false);
      expect(controller.showDetailedQuestions.value, false);
      expect(controller.getAnswer('metaQuest'), false);
    });

    test('should validate meta question is required', () {
      // Without answering meta question
      expect(controller.validateAnswers(), false);

      // After answering NO to meta question
      controller.updateMetaAnswer(false);
      expect(controller.validateAnswers(), true);
    });

    test('should handle metalist answers correctly', () {
      // Test checkbox answer (Type of sound)
      controller.updateAnswer('sound', ['ringing', 'roaring']);
      final soundAnswer = controller.getAnswer('sound') as List<String>?;
      expect(soundAnswer, ['ringing', 'roaring']);

      // Test radio answer (Frequency)
      controller.updateAnswer('frequency', 'constant');
      expect(controller.getAnswer('frequency'), 'constant');

      // Test radio answer (Location)
      controller.updateAnswer('location', 'bothEars');
      expect(controller.getAnswer('location'), 'bothEars');
    });

    test('should clear detailed answers when switching to NO', () {
      // Set up some answers
      controller.updateMetaAnswer(true);
      controller.updateAnswer('sound', ['ringing']);
      controller.updateAnswer('frequency', 'constant');
      controller.updateAnswer('location', 'bothEars');

      // Switch to NO
      controller.updateMetaAnswer(false);

      // Check that only meta question answer remains
      expect(controller.getAnswer('metaQuest'), false);
      expect(controller.getAnswer('sound'), null);
      expect(controller.getAnswer('frequency'), null);
      expect(controller.getAnswer('location'), null);
    });
  });

  group('MetaList Model Tests', () {
    test('should parse MetaList from JSON correctly', () {
      final json = {
        'Label': 'Type of sound you experience (select all that apply):',
        'value': 'sound',
        'options': {
          'type': 'checkbox',
          'subMetaQuest': [
            {'label': 'Ringing', 'value': 'ringing'},
            {'label': 'Roaring', 'value': 'roaring'},
          ]
        }
      };

      final metaList = MetaList.fromJson(json);
      expect(metaList.label, 'Type of sound you experience (select all that apply):');
      expect(metaList.value, 'sound');
      expect(metaList.options.type, 'checkbox');
      expect(metaList.options.subMetaQuest.length, 2);
      expect(metaList.options.subMetaQuest[0].label, 'Ringing');
      expect(metaList.options.subMetaQuest[0].value, 'ringing');
    });

    test('should convert MetaList to JSON correctly', () {
      final metaList = MetaList(
        label: 'Frequency:',
        value: 'frequency',
        options: MetaOptions(
          type: 'radio',
          subMetaQuest: [
            SubMetaQuest(label: 'Constant', value: 'constant'),
            SubMetaQuest(label: 'Intermittent', value: 'intermittent'),
          ],
        ),
      );

      final json = metaList.toJson();
      expect(json['Label'], 'Frequency:');
      expect(json['value'], 'frequency');
      expect(json['options']['type'], 'radio');
      expect(json['options']['subMetaQuest'].length, 2);
    });
  });
}

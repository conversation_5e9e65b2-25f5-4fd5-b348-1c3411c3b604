import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/tfi_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_text_field.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_option_widgets.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class TFIQuestionnaireView extends GetView<TFIQuestionnaireController> {
  const TFIQuestionnaireView({super.key});

  static const String routeName = "/TFIQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildReadOnlyIndicator(),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildMetaQuestion(context, questionnaireSet),
            const SizedBox(height: 24),
            _buildQuestions(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildActionButtons(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This questionnaire has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value &&
          !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed questionnaire. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildDescription(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildMetaQuestion(BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.metaQuest == null || questionnaireSet.metaAnswer == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '*',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  questionnaireSet.metaQuest!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() {
            final selectedValue = controller.metaQuestionAnswer.value;

            return Wrap(
              spacing: 16.0,
              children: questionnaireSet.metaAnswer!.map((metaAnswer) {
                final isSelected = selectedValue == metaAnswer.value;
                final isReadOnly = controller.isReadOnly.value;
                return GestureDetector(
                  onTap: isReadOnly ? null : () {
                    controller.updateMetaAnswer(metaAnswer.value);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: isReadOnly
                          ? Colors.grey.shade200
                          : (isSelected ? Colors.blue.shade100 : Colors.white),
                      border: Border.all(
                        color: isReadOnly
                            ? Colors.grey.shade400
                            : (isSelected ? Colors.blue : Colors.grey.shade300),
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                          size: 20,
                          color: isReadOnly
                              ? Colors.grey.shade500
                              : (isSelected ? Colors.blue : Colors.grey),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          metaAnswer.label,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isReadOnly
                                ? Colors.grey.shade600
                                : (isSelected ? Colors.blue.shade800 : Colors.black87),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          }),
          Obx(() {
            final selectedValue = controller.metaQuestionAnswer.value;
            if (selectedValue == null) {
              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Please select an option',
                  style: TextStyle(
                    color: Colors.red.shade600,
                    fontSize: 12,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
          // Show metalist questions when user selects YES
          Obx(() {
            final selectedValue = controller.metaQuestionAnswer.value;
            if (selectedValue == true) {
              return _buildMetaListQuestions(context, questionnaireSet);
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildQuestions(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Obx(() {
      // Only show detailed questions if user has tinnitus
      if (!controller.showDetailedQuestions.value) {
        return const SizedBox.shrink();
      }

      if (questionnaireSet.data == null || questionnaireSet.data!.isEmpty) {
        return const SizedBox.shrink();
      }

      int questionCounter = 1;
      return Column(
        children: questionnaireSet.data!.map((section) {
          final sectionWidget = _buildSection(context, section, questionnaireSet, questionCounter);
          // Update counter based on number of questions in this section
          if (section.questions != null) {
            questionCounter += section.questions!.length;
          }
          return sectionWidget;
        }).toList(),
      );
    });
  }

  Widget _buildSection(BuildContext context, QuestionnaireSection section, QuestionnaireSet questionnaireSet, int startingQuestionNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            section.label,
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (section.questions != null)
            ...section.questions!.asMap().entries.map((entry) {
              final index = entry.key;
              final question = entry.value;
              return _buildQuestion(context, question, questionnaireSet, startingQuestionNumber + index);
            }),
        ],
      ),
    );
  }

  Widget _buildQuestion(BuildContext context, QuestionnaireQuestion question, QuestionnaireSet questionnaireSet, int questionNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$questionNumber. ${question.title}',
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildAnswerWidget(context, question, questionnaireSet),
        ],
      ),
    );
  }

  Widget _buildAnswerWidget(BuildContext context, QuestionnaireQuestion question, QuestionnaireSet questionnaireSet) {
    // Check if question has scale properties for slider
    if (question.minScale != null && question.maxScale != null) {
      return _buildSliderAnswer(context, question);
    }

    // Check if questionnaire set has options (like APHAB)
    if (questionnaireSet.options != null && questionnaireSet.options!.isNotEmpty) {
      return _buildRadioAnswer(context, question, questionnaireSet.options!);
    }

    // Default to text input
    return _buildTextAnswer(context, question);
  }

  Widget _buildSliderAnswer(BuildContext context, QuestionnaireQuestion question) {
    return Obx(() {
      final currentValue = controller.getAnswer(question.value) as double? ?? question.minScale!.toDouble();

      // Check if this is a percentage question (0-100 range)
      final isPercentageQuestion = question.minScale == 0 && question.maxScale == 100;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(question.startLabel ?? '${question.minScale}', style: const TextStyle(fontSize: 12)),
              Text(
                isPercentageQuestion
                  ? currentValue.toStringAsFixed(2)
                  : currentValue.toInt().toString(),
                style: const TextStyle(fontWeight: FontWeight.bold)
              ),
              Text(question.endLabel ?? '${question.maxScale}', style: const TextStyle(fontSize: 12)),
            ],
          ),
          Slider(
            value: currentValue,
            min: question.minScale!.toDouble(),
            max: question.maxScale!.toDouble(),
            divisions: isPercentageQuestion ? 10000 : (question.maxScale! - question.minScale!), // More divisions for percentage questions
            onChanged: controller.isReadOnly.value ? null : (value) {
              // Round percentage values to 2 decimal places
              final roundedValue = isPercentageQuestion
                ? double.parse(value.toStringAsFixed(2))
                : value;
              controller.updateAnswer(question.value, roundedValue);
            },
          ),
        ],
      );
    });
  }

  Widget _buildRadioAnswer(BuildContext context, QuestionnaireQuestion question, List<QuestionnaireOption> options) {
    return Obx(() {
      final selectedValue = controller.getAnswer(question.value);

      return Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: options.map((option) {
          final isSelected = selectedValue == option.value;
          final isReadOnly = controller.isReadOnly.value;
          return GestureDetector(
            onTap: isReadOnly ? null : () {
              controller.updateAnswer(question.value, option.value);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isReadOnly
                    ? Colors.grey.shade200
                    : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
                border: Border.all(
                  color: isReadOnly
                      ? Colors.grey.shade400
                      : (isSelected ? Colors.blue : Colors.grey.shade300),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                    size: 16,
                    color: isReadOnly
                        ? Colors.grey.shade500
                        : (isSelected ? Colors.blue : Colors.grey),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    option.label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: isReadOnly
                          ? Colors.grey.shade600
                          : (isSelected ? Colors.blue.shade800 : Colors.black87),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      );
    });
  }

  Widget _buildTextAnswer(BuildContext context, QuestionnaireQuestion question) {
    return TextFormField(
      initialValue: controller.getAnswer(question.value)?.toString() ?? '',
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      decoration: InputDecoration(
        hintText: 'Enter your answer',
        hintStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        isDense: true,
      ),
      onChanged: (value) {
        controller.updateAnswer(question.value, value);
      },
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        // Show Submit button when in edit mode
        return ReusableButton(
          width: context.width * .22,
          title: 'Submit',
          onTap: controller.submitQuestionnaire,
          isLoading: controller.isSubmitting.value,
        );
      }
    });
  }

  Widget _buildMetaListQuestions(BuildContext context, QuestionnaireSet questionnaireSet) {
    // Find the metaAnswer with value true (YES answer)
    final yesMetaAnswer = questionnaireSet.metaAnswer?.firstWhere(
      (metaAnswer) => metaAnswer.value == true,
      orElse: () => questionnaireSet.metaAnswer!.first,
    );

    if (yesMetaAnswer?.metalist == null || yesMetaAnswer!.metalist!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.05),
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: yesMetaAnswer.metalist!.map((metaList) {
          return _buildMetaListQuestion(context, metaList);
        }).toList(),
      ),
    );
  }

  Widget _buildMetaListQuestion(BuildContext context, MetaList metaList) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '*',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  metaList.label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.charcoalBlue,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (metaList.options.type == 'checkbox')
            _buildCheckboxOptions(context, metaList)
          else if (metaList.options.type == 'radio')
            _buildRadioOptions(context, metaList),
        ],
      ),
    );
  }

  Widget _buildCheckboxOptions(BuildContext context, MetaList metaList) {
    return Obx(() {
      final selectedValues = controller.getAnswer(metaList.value) as List<String>? ?? [];
      final isReadOnly = controller.isReadOnly.value;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ReusableCheckboxGroup(
            labels: metaList.options.subMetaQuest.map((option) => option.label).toList(),
            values: metaList.options.subMetaQuest.map((option) => option.value).toList(),
            selectedValues: selectedValues,
            isReadOnly: isReadOnly,
            onChanged: (newValues) {
              // Check if "other" was deselected
              if (!newValues.contains('other') && selectedValues.contains('other')) {
                controller.updateAnswer('${metaList.value}_other_text', '');
              }
              controller.updateAnswer(metaList.value, newValues);
            },
          ),
          // Show text field when "other" is selected
          if (selectedValues.contains('other'))
            Container(
              margin: const EdgeInsets.only(top: 12),
              child: _buildOtherTextField(context, metaList),
            ),
        ],
      );
    });
  }

  Widget _buildRadioOptions(BuildContext context, MetaList metaList) {
    return Obx(() {
      final selectedValue = controller.getAnswer(metaList.value) as String?;
      final isReadOnly = controller.isReadOnly.value;

      return ReusableRadioGroup(
        labels: metaList.options.subMetaQuest.map((option) => option.label).toList(),
        values: metaList.options.subMetaQuest.map((option) => option.value).toList(),
        selectedValue: selectedValue,
        isReadOnly: isReadOnly,
        useWrapLayout: true,
        onChanged: (value) {
          controller.updateAnswer(metaList.value, value);
        },
      );
    });
  }

  Widget _buildOtherTextField(BuildContext context, MetaList metaList) {
    final textFieldKey = '${metaList.value}_other_text';

    return Obx(() {
      final currentText = controller.getAnswer(textFieldKey) as String? ?? '';
      final isReadOnly = controller.isReadOnly.value;

      // Create a controller for the text field
      final textController = TextEditingController(text: currentText);

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '*',
                  style: TextStyle(
                    color: Colors.red.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  'Please specify:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ReusableTextField(
              controller: textController,
              hintText: 'Enter the type of sound you experience...',
              readOnly: isReadOnly,
              maxLines: 2,
              borderRadius: 6,
              onChanged: (value) {
                controller.updateAnswer(textFieldKey, value);
              },
            ),
          ],
        ),
      );
    });
  }
}

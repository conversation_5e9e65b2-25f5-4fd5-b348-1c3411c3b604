import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'base_questionnaire_controller.dart';
import '../view/tfi_questionnaire_view.dart';

class TFIQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.tfiQuest;

  @override
  String get questionnaireKey => 'setThree'; // TFI is in setThree based on the demo data

  @override
  String get routeName => TFIQuestionnaireView.routeName;

  // Observable for tracking meta question answer
  final Rxn<bool> metaQuestionAnswer = Rxn<bool>();

  // Observable for showing detailed questions
  final RxBool showDetailedQuestions = false.obs;

  void updateMetaAnswer(bool hasExperienceTinnitus) {
    metaQuestionAnswer.value = hasExperienceTinnitus;
    updateAnswer('metaQuest', hasExperienceTinnitus);

    if (hasExperienceTinnitus) {
      // User has tinnitus, show detailed questions
      showDetailedQuestions.value = true;
      _initializeDetailedQuestionsWithDefaults();
    } else {
      // User doesn't have tinnitus, hide detailed questions and clear any previous answers
      showDetailedQuestions.value = false;
      _clearDetailedAnswers();
    }
  }

  void _clearDetailedAnswers() {
    // Clear all answers except the meta question answer
    final metaAnswer = answers['metaQuest'];
    answers.clear();
    answers['metaQuest'] = metaAnswer;

    // Also clear any metalist answers
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.metaAnswer != null) {
      final yesMetaAnswer = questionnaireSet!.metaAnswer!.firstWhere(
        (metaAnswer) => metaAnswer.value == true,
        orElse: () => questionnaireSet.metaAnswer!.first,
      );

      if (yesMetaAnswer.metalist != null) {
        for (final metaList in yesMetaAnswer.metalist!) {
          answers.remove(metaList.value);
        }
      }
    }

    answers.refresh();
  }

  void _initializeDetailedQuestionsWithDefaults() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.data == null) {
      return;
    }

    // Initialize all questions with default values if they don't already have answers
    for (final section in questionnaireSet!.data!) {
      if (section.questions != null) {
        for (final question in section.questions!) {
          // Only initialize if the question doesn't already have an answer
          if (getAnswer(question.value) == null) {
            double defaultValue;

            // Use the question's minScale if available, otherwise use 0
            if (question.minScale != null) {
              defaultValue = question.minScale!.toDouble();
            } else {
              defaultValue = 0.0;
            }

            // Round percentage values to 2 decimal places
            if (question.minScale == 0 && question.maxScale == 100) {
              defaultValue = double.parse(defaultValue.toStringAsFixed(2));
            }

            updateAnswer(question.value, defaultValue);
          }
        }
      }
    }
  }

  @override
  bool validateAnswers() {
    // First check if meta question is answered
    if (metaQuestionAnswer.value == null) {
      reusableSnackBar(message: 'Please answer the tinnitus experience question');
      return false;
    }

    // If user doesn't have tinnitus, only meta question needs to be answered
    if (metaQuestionAnswer.value == false) {
      return true;
    }

    // If user has tinnitus, validate metalist questions first
    if (!_validateMetaListQuestions()) {
      return false;
    }

    // If user has tinnitus, validate all detailed questions
    return super.validateAnswers();
  }

  bool _validateMetaListQuestions() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.metaAnswer == null) return true;

    // Find the YES metaAnswer
    final yesMetaAnswer = questionnaireSet!.metaAnswer!.firstWhere(
      (metaAnswer) => metaAnswer.value == true,
      orElse: () => questionnaireSet.metaAnswer!.first,
    );

    if (yesMetaAnswer.metalist == null || yesMetaAnswer.metalist!.isEmpty) {
      return true;
    }

    // Validate each metalist question
    for (final metaList in yesMetaAnswer.metalist!) {
      final answer = getAnswer(metaList.value);

      if (metaList.options.type == 'checkbox') {
        // For checkbox, ensure at least one option is selected
        if (answer == null || (answer is List && answer.isEmpty)) {
          reusableSnackBar(message: 'Please select at least one option for: ${metaList.label}');
          return false;
        }
      } else if (metaList.options.type == 'radio') {
        // For radio, ensure an option is selected
        if (answer == null || answer.toString().isEmpty) {
          reusableSnackBar(message: 'Please select an option for: ${metaList.label}');
          return false;
        }
      }
    }

    return true;
  }

  @override
  Future<void> loadSavedAnswers() async {
    await super.loadSavedAnswers();

    // Load saved meta answer
    final savedMetaAnswer = getAnswer('metaQuest');
    if (savedMetaAnswer != null && savedMetaAnswer is bool) {
      metaQuestionAnswer.value = savedMetaAnswer;
      showDetailedQuestions.value = savedMetaAnswer;

      // If user previously answered YES, initialize detailed questions with defaults
      if (savedMetaAnswer == true) {
        _initializeDetailedQuestionsWithDefaults();
      }
    }
  }

  @override
  void onFormReset() {
    // Reset TFI-specific state
    metaQuestionAnswer.value = null;
    showDetailedQuestions.value = false;
  }
}
